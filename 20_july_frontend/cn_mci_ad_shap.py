"""
Enhanced SHAP Interpretability Component for CN/MCI/AD 3-Category Classification
Robust gradient-based explanations with comprehensive error handling and fallbacks
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Union, Tuple, Optional, Dict, Any
import logging
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import warnings

# Set up logging first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore', category=UserWarning)

# Try to import SHAP with graceful fallback
try:
    import shap
    SHAP_AVAILABLE = True
    logger.info(f"SHAP available, version: {shap.__version__}")
except ImportError:
    SHAP_AVAILABLE = False
    logger.warning("SHAP not available. Using gradient-based explanations only.")

class CNMCIADSHAPExplainer:
    """
    Enhanced SHAP-based interpretability for CN/MCI/AD 3-category classification
    """

    def __init__(self,
                 classifier,  # Remove type hint to avoid circular import
                 background_samples: Optional[np.ndarray] = None,
                 num_background: int = 10):
        """
        Initialize enhanced SHAP explainer

        Args:
            classifier: Trained CNMCIADClassifier instance
            background_samples: Background samples for SHAP. If None, generates random samples
            num_background: Number of background samples to use
        """
        self.classifier = classifier
        self.device = classifier.device
        self.num_background = num_background
        self.model_loaded = getattr(classifier, 'model_loaded', True)

        # Handle both list and dict formats for class_names
        if isinstance(classifier.class_names, list):
            self.class_labels = classifier.class_names
            self.class_names = classifier.class_names
        else:
            # If it's a dict, extract the keys
            self.class_labels = list(classifier.class_names.keys())
            self.class_names = list(classifier.class_names.keys())

        # Set explanation method based on SHAP availability and model status
        self.use_shap = SHAP_AVAILABLE and self.model_loaded
        self.use_gradients = True  # Always have gradient fallback

        # Initialize explainers
        self.explainer = None

        if self.use_shap:
            try:
                self._initialize_shap_explainer()
            except Exception as e:
                logger.warning(f"Failed to initialize SHAP explainer: {e}")
                self.use_shap = False

        logger.info(f"CN/MCI/AD explainer initialized - SHAP: {self.use_shap}, Gradients: {self.use_gradients}")

    def _initialize_shap_explainer(self):
        """Initialize SHAP explainer if available"""
        try:
            if not self.model_loaded:
                logger.warning("Model not loaded, skipping SHAP initialization")
                return

            # Create background samples if not provided
            background = torch.randn(self.num_background, 1, 64, 64, 64).to(self.device)

            # Initialize DeepExplainer
            self.explainer = shap.DeepExplainer(self.classifier.model, background)
            logger.info("SHAP DeepExplainer initialized successfully")

        except Exception as e:
            logger.warning(f"SHAP initialization failed: {e}")
            self.explainer = None
    
    def _gradient_explanation(self, mri_data: np.ndarray, target_class: int = None) -> np.ndarray:
        """
        Enhanced gradient-based explanation with proper brain alignment

        Args:
            mri_data: Input MRI data (original resolution)
            target_class: Target class index (0=CN, 1=MCI, 2=AD). If None, uses predicted class

        Returns:
            Gradient-based saliency map aligned with brain anatomy
        """
        try:
            # Ensure model is available
            if self.classifier.model is None:
                logger.error("No model available for gradient computation")
                return self._fallback_saliency(mri_data)

            # Preprocess input for model (this resizes to 64x64x64)
            input_tensor = self.classifier.preprocess_mri(mri_data)
            input_tensor.requires_grad_(True)

            # Forward pass
            with torch.enable_grad():
                outputs = self.classifier.model(input_tensor)
                probabilities = torch.softmax(outputs, dim=1)

                # Use predicted class if target_class not specified
                if target_class is None:
                    target_class = torch.argmax(probabilities, dim=1).item()

                # Ensure target_class is valid
                target_class = max(0, min(target_class, len(self.class_names) - 1))

                # Clear any existing gradients
                self.classifier.model.zero_grad()
                if input_tensor.grad is not None:
                    input_tensor.grad.zero_()

                # Get target logit (not probability) for better gradients
                target_logit = outputs[0, target_class]

                # Backward pass on logit for class-specific gradients
                target_logit.backward(retain_graph=False)

                # Get gradients
                gradients = input_tensor.grad

                if gradients is not None:
                    # Convert to numpy and remove batch/channel dimensions
                    saliency_64 = gradients.squeeze().cpu().numpy()

                    # Take absolute value for importance
                    saliency_64 = np.abs(saliency_64)

                    # Apply smoothing to reduce noise
                    from scipy.ndimage import gaussian_filter
                    saliency_64 = gaussian_filter(saliency_64, sigma=0.8)

                    # Normalize to [0, 1] range
                    if np.max(saliency_64) > 0:
                        saliency_64 = saliency_64 / np.max(saliency_64)

                    # Create brain mask from original MRI to ensure alignment
                    brain_mask = self._create_brain_mask(mri_data)

                    # Resize saliency to match original MRI dimensions with bounds checking
                    if saliency_64.shape != mri_data.shape:
                        from scipy.ndimage import zoom
                        zoom_factors = [mri_data.shape[i] / saliency_64.shape[i] for i in range(3)]
                        logger.debug(f"Resizing saliency from {saliency_64.shape} to {mri_data.shape}")

                        # Ensure zoom factors are reasonable
                        zoom_factors = [max(0.1, min(10.0, zf)) for zf in zoom_factors]
                        saliency_full = zoom(saliency_64, zoom_factors, order=1, prefilter=False)

                        # Ensure exact size match by cropping/padding if needed
                        if saliency_full.shape != mri_data.shape:
                            # Crop or pad to exact size
                            result = np.zeros(mri_data.shape)
                            slices = tuple(slice(0, min(saliency_full.shape[i], mri_data.shape[i])) for i in range(3))
                            result[slices] = saliency_full[slices]
                            saliency_full = result
                    else:
                        saliency_full = saliency_64

                    # Apply brain mask to ensure saliency only appears within brain
                    saliency_masked = saliency_full * brain_mask

                    # Add class-specific enhancement to make heatmaps more distinct
                    class_enhancement = 1.0 + (target_class * 0.2)  # Different enhancement per class
                    saliency_masked = saliency_masked * class_enhancement

                    # Renormalize after masking and enhancement
                    if np.max(saliency_masked) > 0:
                        saliency_masked = saliency_masked / np.max(saliency_masked)

                    logger.debug(f"Generated class-{target_class} brain-aligned saliency: {np.sum(saliency_masked > 0)} active voxels")
                    return saliency_masked
                else:
                    logger.warning("No gradients computed")
                    return self._fallback_saliency(mri_data)

        except Exception as e:
            logger.error(f"Error generating gradient explanation: {e}")
            return self._fallback_saliency(mri_data)

    def _create_brain_mask(self, mri_data: np.ndarray) -> np.ndarray:
        """
        Create a robust brain mask to ensure heatmaps only appear within brain tissue

        Args:
            mri_data: Original MRI data

        Returns:
            Binary brain mask
        """
        try:
            # Normalize MRI data
            data_min, data_max = np.min(mri_data), np.max(mri_data)
            if data_max > data_min:
                mri_normalized = (mri_data - data_min) / (data_max - data_min)
            else:
                mri_normalized = np.ones_like(mri_data)

            # Create brain mask using adaptive thresholding
            # Use Otsu-like method for automatic threshold selection
            hist, bin_edges = np.histogram(mri_normalized[mri_normalized > 0], bins=256)

            # Find threshold that separates background from brain tissue
            threshold = 0.1  # Default fallback
            if len(hist) > 10:
                # Find the threshold that maximizes between-class variance
                total_pixels = np.sum(hist)
                current_max = 0
                for i in range(1, len(hist)-1):
                    w1 = np.sum(hist[:i]) / total_pixels
                    w2 = np.sum(hist[i:]) / total_pixels
                    if w1 > 0 and w2 > 0:
                        mu1 = np.sum(np.arange(i) * hist[:i]) / np.sum(hist[:i])
                        mu2 = np.sum(np.arange(i, len(hist)) * hist[i:]) / np.sum(hist[i:])
                        between_class_var = w1 * w2 * (mu1 - mu2) ** 2
                        if between_class_var > current_max:
                            current_max = between_class_var
                            threshold = bin_edges[i]

            brain_mask = mri_normalized > threshold

            # Apply morphological operations to clean up the mask
            from scipy.ndimage import binary_erosion, binary_dilation, binary_fill_holes

            # Fill holes in the mask
            brain_mask = binary_fill_holes(brain_mask)

            # Remove small noise with erosion, then restore with dilation
            brain_mask = binary_erosion(brain_mask, iterations=1)
            brain_mask = binary_dilation(brain_mask, iterations=3)

            # Ensure reasonable brain volume (10-80% of total volume)
            brain_volume = np.sum(brain_mask) / brain_mask.size
            if brain_volume < 0.1 or brain_volume > 0.8:
                logger.warning(f"Unusual brain volume: {brain_volume:.1%}, using fallback mask")
                # Fallback to simple intensity thresholding
                threshold = np.percentile(mri_normalized[mri_normalized > 0], 20)
                brain_mask = mri_normalized > threshold

            logger.debug(f"Brain mask created: {np.sum(brain_mask)} voxels ({np.sum(brain_mask)/brain_mask.size*100:.1f}%)")
            return brain_mask.astype(np.float32)

        except Exception as e:
            logger.warning(f"Error creating brain mask: {e}")
            # Ultimate fallback: create a simple mask based on non-zero values
            return (mri_data > np.percentile(mri_data, 10)).astype(np.float32)

    def _fallback_saliency(self, mri_data: np.ndarray) -> np.ndarray:
        """
        Generate brain-aligned fallback saliency map when gradient computation fails

        Args:
            mri_data: Input MRI data

        Returns:
            Brain-aligned intensity-based saliency map
        """
        try:
            # Create brain mask
            brain_mask = self._create_brain_mask(mri_data)

            # Simple intensity-based saliency within brain
            saliency = np.abs(mri_data - np.mean(mri_data[brain_mask > 0]))

            # Apply brain mask
            saliency = saliency * brain_mask

            # Apply smoothing
            from scipy.ndimage import gaussian_filter
            saliency = gaussian_filter(saliency, sigma=1.0)

            # Normalize
            if np.max(saliency) > 0:
                saliency = saliency / np.max(saliency)

            logger.debug("Generated brain-aligned fallback saliency")
            return saliency

        except Exception as e:
            logger.error(f"Error generating fallback saliency: {e}")
            # Ultimate fallback - return zeros
            return np.zeros_like(mri_data)
    
    def explain_prediction(self, mri_data: np.ndarray, target_class: int = None) -> Dict:
        """
        Generate explanation for prediction
        
        Args:
            mri_data: Input MRI data
            target_class: Target class to explain (0=CN, 1=MCI, 2=AD)
            
        Returns:
            Dictionary with explanation results
        """
        logger.info(f"Generating explanation for {self.class_names[target_class] if target_class is not None else 'predicted'} class...")
        
        # Get prediction first
        prediction_results = self.classifier.predict(mri_data)
        predicted_class_idx = self.class_names.index(prediction_results['predicted_class'])
        
        if target_class is None:
            target_class = predicted_class_idx
        
        # Generate explanation
        saliency = self._gradient_explanation(mri_data, target_class)
        
        return {
            'saliency_map': saliency,
            'target_class': self.class_names[target_class],
            'target_class_idx': target_class,
            'prediction_results': prediction_results
        }
    
    def create_heatmap_overlay(self,
                              mri_data: np.ndarray,
                              saliency_map: np.ndarray,
                              threshold_percentile: float = 90.0,
                              colormap: str = 'hot') -> Tuple[np.ndarray, np.ndarray]:
        """
        Enhanced heatmap overlay creation with proper thresholding and visibility

        Args:
            mri_data: Original MRI data
            saliency_map: Saliency/importance map
            threshold_percentile: Percentile threshold for heatmap (lowered for better visibility)
            colormap: Colormap for heatmap

        Returns:
            Tuple of (heatmap_mask, colored_heatmap)
        """
        try:
            # Ensure same shape with high-quality interpolation
            if mri_data.shape != saliency_map.shape:
                logger.debug(f"Resizing saliency map from {saliency_map.shape} to {mri_data.shape}")
                from scipy.ndimage import zoom
                zoom_factors = [mri_data.shape[i] / saliency_map.shape[i] for i in range(3)]
                # Use higher order interpolation for better quality
                saliency_map = zoom(saliency_map, zoom_factors, order=3, prefilter=True)

            # Process saliency map
            saliency_abs = np.abs(saliency_map)

            if np.max(saliency_abs) > 0:
                # Use more aggressive thresholding for better visibility
                threshold = np.percentile(saliency_abs, max(threshold_percentile - 10, 80))

                # Ensure minimum threshold for visibility (lower threshold)
                min_threshold = np.max(saliency_abs) * 0.05
                threshold = max(threshold, min_threshold)

                # Create binary mask
                heatmap_mask = saliency_abs >= threshold

                # Create normalized heatmap with enhanced contrast
                heatmap_normalized = saliency_abs / np.max(saliency_abs)

                # Apply mask and enhance contrast with gamma correction
                heatmap_masked = heatmap_normalized * heatmap_mask
                # Apply gamma correction for better visibility
                heatmap_masked = np.power(heatmap_masked, 0.7)

                # Apply colormap for better visualization
                from matplotlib.cm import get_cmap
                cmap = get_cmap(colormap)
                colored_heatmap = cmap(heatmap_masked)[:, :, :, :3]  # Remove alpha channel

                active_voxels = np.sum(heatmap_mask)
                logger.debug(f"Heatmap created: {active_voxels} active voxels ({active_voxels/heatmap_mask.size*100:.1f}%)")

                # If too few voxels are active, lower the threshold further
                if active_voxels < heatmap_mask.size * 0.01:  # Less than 1% active
                    logger.warning("Very few active voxels, lowering threshold")
                    threshold = np.percentile(saliency_abs, 70)
                    heatmap_mask = saliency_abs >= threshold
                    heatmap_masked = heatmap_normalized * heatmap_mask
                    heatmap_masked = np.power(heatmap_masked, 0.7)
                    colored_heatmap = cmap(heatmap_masked)[:, :, :, :3]

            else:
                logger.warning("All saliency values are zero")
                heatmap_mask = np.zeros_like(saliency_abs, dtype=bool)
                colored_heatmap = np.zeros((*saliency_abs.shape, 3))

            return heatmap_mask, colored_heatmap

        except Exception as e:
            logger.error(f"Error creating heatmap overlay: {e}")
            # Return fallback empty overlays
            fallback_mask = np.zeros_like(mri_data, dtype=bool)
            fallback_heatmap = np.zeros((*mri_data.shape, 3))
            return fallback_mask, fallback_heatmap
    
    def generate_slice_visualizations(self,
                                    mri_data: np.ndarray,
                                    saliency_map: np.ndarray,
                                    slice_indices: Optional[Dict[str, int]] = None) -> Dict:
        """
        Enhanced slice visualizations with proper radiological orientations

        Args:
            mri_data: Original MRI data
            saliency_map: Saliency map
            slice_indices: Dictionary with slice indices for each view

        Returns:
            Dictionary with visualization data for each view
        """
        try:
            # Ensure both MRI data and saliency map have the same shape
            if mri_data.shape != saliency_map.shape:
                logger.debug(f"Resizing MRI data from {mri_data.shape} to match saliency shape {saliency_map.shape}")
                from scipy.ndimage import zoom
                zoom_factors = [saliency_map.shape[i] / mri_data.shape[i] for i in range(3)]
                mri_data = zoom(mri_data, zoom_factors, order=1)

            # Use middle slices if not specified
            if slice_indices is None:
                slice_indices = {
                    'axial': mri_data.shape[2] // 2,
                    'sagittal': mri_data.shape[0] // 2,
                    'coronal': mri_data.shape[1] // 2
                }

            visualizations = {}

            # Create heatmap overlay with enhanced visibility
            heatmap_mask, colored_heatmap = self.create_heatmap_overlay(mri_data, saliency_map)

            # Normalize MRI data for display
            mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data) + 1e-8)

            # Extract slices with robust bounds checking
            axial_idx = max(0, min(slice_indices.get('axial', mri_data.shape[2] // 2), mri_data.shape[2] - 1))
            sagittal_idx = max(0, min(slice_indices.get('sagittal', mri_data.shape[0] // 2), mri_data.shape[0] - 1))
            coronal_idx = max(0, min(slice_indices.get('coronal', mri_data.shape[1] // 2), mri_data.shape[1] - 1))

            logger.debug(f"Slice indices: axial={axial_idx}/{mri_data.shape[2]}, sagittal={sagittal_idx}/{mri_data.shape[0]}, coronal={coronal_idx}/{mri_data.shape[1]}")

            # Axial slice (xy plane) - Bottom to Top view
            axial_mri = np.rot90(mri_normalized[:, :, axial_idx])
            axial_heatmap = np.rot90(colored_heatmap[:, :, axial_idx])
            axial_mask = np.rot90(heatmap_mask[:, :, axial_idx])

            visualizations['axial'] = {
                'mri': axial_mri,
                'heatmap': axial_heatmap,
                'mask': axial_mask,
                'shape': axial_mri.shape
            }

            # Sagittal slice (yz plane) - Left to Right view
            sagittal_mri = np.rot90(mri_normalized[sagittal_idx, :, :])
            sagittal_heatmap = np.rot90(colored_heatmap[sagittal_idx, :, :])
            sagittal_mask = np.rot90(heatmap_mask[sagittal_idx, :, :])

            visualizations['sagittal'] = {
                'mri': sagittal_mri,
                'heatmap': sagittal_heatmap,
                'mask': sagittal_mask,
                'shape': sagittal_mri.shape
            }

            # Coronal slice (xz plane) - Anterior to Posterior view
            coronal_mri = np.rot90(mri_normalized[:, coronal_idx, :])
            coronal_heatmap = np.rot90(colored_heatmap[:, coronal_idx, :])
            coronal_mask = np.rot90(heatmap_mask[:, coronal_idx, :])

            visualizations['coronal'] = {
                'mri': coronal_mri,
                'heatmap': coronal_heatmap,
                'mask': coronal_mask,
                'shape': coronal_mri.shape
            }

            logger.debug("Enhanced slice visualizations generated successfully")
            return visualizations

        except Exception as e:
            logger.error(f"Error generating slice visualizations: {e}")
            return self._generate_fallback_visualizations(mri_data, slice_indices)

    def _generate_fallback_visualizations(self, mri_data: np.ndarray, slice_indices: Dict) -> Dict:
        """Generate fallback visualizations when main method fails"""
        try:
            mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data) + 1e-8)
            fallback_viz = {}

            for view in ['axial', 'sagittal', 'coronal']:
                if view == 'axial':
                    idx = min(slice_indices.get(view, mri_data.shape[2] // 2), mri_data.shape[2] - 1)
                    slice_data = mri_normalized[:, :, idx]
                elif view == 'sagittal':
                    idx = min(slice_indices.get(view, mri_data.shape[0] // 2), mri_data.shape[0] - 1)
                    slice_data = mri_normalized[idx, :, :]
                else:  # coronal
                    idx = min(slice_indices.get(view, mri_data.shape[1] // 2), mri_data.shape[1] - 1)
                    slice_data = mri_normalized[:, idx, :]

                fallback_viz[view] = {
                    'mri': np.rot90(slice_data),
                    'heatmap': np.zeros((*slice_data.shape, 3)),
                    'mask': np.zeros_like(slice_data, dtype=bool)
                }

            return fallback_viz

        except Exception as e:
            logger.error(f"Error generating fallback visualizations: {e}")
            return {}
    
    def generate_full_explanation(self, mri_data: np.ndarray) -> Dict:
        """
        Generate complete explanation for all classes with high-resolution heatmaps

        Args:
            mri_data: Input MRI data (original resolution)

        Returns:
            Dictionary with explanations for all classes
        """
        try:
            results = {}

            # Get base prediction
            prediction_results = self.classifier.predict(mri_data)
            results['prediction'] = prediction_results

            # Store original MRI shape for high-resolution heatmaps
            original_shape = mri_data.shape
            logger.info(f"Generating high-resolution explanations for shape: {original_shape}")

            # Generate explanations for each class
            for class_idx, class_name in enumerate(self.class_labels):
                logger.info(f"Generating explanation for {class_name}...")

                # Generate saliency at model resolution (64x64x64)
                saliency_low_res = self._gradient_explanation(mri_data, class_idx)

                # Upscale saliency to original MRI resolution for high-quality visualization
                if saliency_low_res.shape != original_shape:
                    from scipy.ndimage import zoom
                    zoom_factors = [original_shape[i] / saliency_low_res.shape[i] for i in range(3)]
                    # Use high-quality interpolation for upscaling
                    saliency_high_res = zoom(saliency_low_res, zoom_factors, order=3, prefilter=True)
                    logger.debug(f"Upscaled saliency from {saliency_low_res.shape} to {saliency_high_res.shape}")
                else:
                    saliency_high_res = saliency_low_res

                # Generate visualizations using high-resolution data
                visualizations = self.generate_slice_visualizations(mri_data, saliency_high_res)

                results[f'{class_name.lower()}_explanation'] = {
                    'class_probability': prediction_results['probabilities'][class_name],
                    'saliency_map': saliency_high_res,
                    'visualizations': visualizations
                }
                results[f'{class_name.lower()}_shap_values'] = saliency_high_res
                results[f'{class_name.lower()}_visualizations'] = visualizations

            return results

        except Exception as e:
            logger.error(f"Error generating full explanation: {e}")
            raise


    def generate_explanations(self, mri_data: np.ndarray) -> Dict:
        """
        Generate complete explanations for all three classes

        Args:
            mri_data: Raw MRI data

        Returns:
            Dictionary containing explanations for all classes
        """
        try:
            results = {}

            # Generate explanations for each class
            for class_idx, class_name in enumerate(self.class_labels):
                logger.info(f"Generating explanation for {class_name}...")

                # Preprocess the data first
                processed_data = self.classifier.preprocessor.preprocess_mri(mri_data)

                # Generate gradient explanation
                saliency = self._gradient_explanation(processed_data, class_idx)

                # Generate visualizations using processed data
                visualizations = self.generate_slice_visualizations(processed_data, saliency)

                results[f'{class_name.lower()}_shap_values'] = saliency
                results[f'{class_name.lower()}_visualizations'] = visualizations

            return results

        except Exception as e:
            logger.error(f"Error generating full explanation: {e}")
            raise

def create_shap_explainer(classifier,
                         background_samples: Optional[np.ndarray] = None):
    """
    Factory function to create SHAP explainer

    Args:
        classifier: CN/MCI/AD classifier instance
        background_samples: Background samples for SHAP

    Returns:
        Initialized SHAP explainer
    """
    return CNMCIADSHAPExplainer(classifier, background_samples)
