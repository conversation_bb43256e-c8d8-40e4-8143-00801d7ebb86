#!/usr/bin/env python3
"""
Comprehensive test script for the enhanced CN/MCI/AD classification system
Tests all components: preprocessing, model loading, inference, and SHAP explanations
"""

import sys
import numpy as np
import logging
from pathlib import Path
import tempfile
import nibabel as nib

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_preprocessing():
    """Test the enhanced preprocessing pipeline"""
    logger.info("Testing preprocessing pipeline...")
    
    try:
        from cn_mci_ad_preprocessing import create_preprocessor
        
        # Create preprocessor
        preprocessor = create_preprocessor()
        
        # Create dummy MRI data
        dummy_data = np.random.randn(128, 128, 128) * 100 + 500
        
        # Test with temporary NIfTI file
        with tempfile.NamedTemporaryFile(suffix='.nii', delete=False) as tmp_file:
            img = nib.Nifti1Image(dummy_data, np.eye(4))
            nib.save(img, tmp_file.name)
            
            # Test preprocessing
            result = preprocessor.preprocess_scan(tmp_file.name)
            
            if result is not None:
                logger.info(f"✅ Preprocessing test passed! Output shape: {result.shape}")
                logger.info(f"   Intensity range: [{np.min(result):.3f}, {np.max(result):.3f}]")
                return True
            else:
                logger.error("❌ Preprocessing test failed!")
                return False
                
    except Exception as e:
        logger.error(f"❌ Preprocessing test error: {e}")
        return False

def test_model_loading():
    """Test model loading and inference"""
    logger.info("Testing model loading and inference...")
    
    try:
        from cn_mci_ad_model import create_classifier
        
        # Create classifier
        classifier = create_classifier()
        
        # Test with dummy data
        dummy_mri = np.random.randn(64, 64, 64)
        
        # Test prediction
        results = classifier.predict(dummy_mri)
        
        if results and 'predicted_class' in results:
            logger.info(f"✅ Model test passed!")
            logger.info(f"   Predicted class: {results['predicted_class']}")
            logger.info(f"   Confidence: {results['confidence']:.3f}")
            logger.info(f"   Model loaded: {classifier.model_loaded}")
            return True
        else:
            logger.error("❌ Model test failed!")
            return False
            
    except Exception as e:
        logger.error(f"❌ Model test error: {e}")
        return False

def test_shap_explanations():
    """Test SHAP explanation generation"""
    logger.info("Testing SHAP explanations...")
    
    try:
        from cn_mci_ad_model import create_classifier
        from cn_mci_ad_shap import create_shap_explainer
        
        # Create components
        classifier = create_classifier()
        explainer = create_shap_explainer(classifier)
        
        # Test with dummy data
        dummy_mri = np.random.randn(64, 64, 64)
        
        # Test explanation generation
        explanation = explainer.generate_full_explanation(dummy_mri)
        
        if explanation:
            logger.info(f"✅ SHAP test passed!")
            logger.info(f"   Explanation keys: {list(explanation.keys())}")
            
            # Check for class-specific explanations
            for class_name in classifier.class_names:
                viz_key = f'{class_name.lower()}_visualizations'
                if viz_key in explanation:
                    viz = explanation[viz_key]
                    logger.info(f"   {class_name} visualization views: {list(viz.keys())}")
            
            return True
        else:
            logger.error("❌ SHAP test failed!")
            return False
            
    except Exception as e:
        logger.error(f"❌ SHAP test error: {e}")
        return False

def test_file_format_support():
    """Test support for different file formats"""
    logger.info("Testing file format support...")
    
    try:
        from cn_mci_ad_preprocessing import create_preprocessor
        
        preprocessor = create_preprocessor()
        dummy_data = np.random.randn(64, 64, 64)
        
        # Test numpy format
        with tempfile.NamedTemporaryFile(suffix='.npy', delete=False) as tmp_file:
            np.save(tmp_file.name, dummy_data)
            result = preprocessor.load_mri_file(tmp_file.name)
            
            if result is not None:
                logger.info("✅ NumPy format support working")
            else:
                logger.error("❌ NumPy format support failed")
                return False
        
        # Test NIfTI format
        with tempfile.NamedTemporaryFile(suffix='.nii', delete=False) as tmp_file:
            img = nib.Nifti1Image(dummy_data, np.eye(4))
            nib.save(img, tmp_file.name)
            result = preprocessor.load_mri_file(tmp_file.name)
            
            if result is not None:
                logger.info("✅ NIfTI format support working")
                return True
            else:
                logger.error("❌ NIfTI format support failed")
                return False
                
    except Exception as e:
        logger.error(f"❌ File format test error: {e}")
        return False

def test_error_handling():
    """Test error handling with invalid inputs"""
    logger.info("Testing error handling...")
    
    try:
        from cn_mci_ad_model import create_classifier
        from cn_mci_ad_preprocessing import create_preprocessor
        
        classifier = create_classifier()
        preprocessor = create_preprocessor()
        
        # Test with invalid data shapes
        invalid_data = np.random.randn(10, 10)  # 2D instead of 3D
        
        # Should handle gracefully
        if not preprocessor.validate_input(invalid_data):
            logger.info("✅ Input validation working correctly")
        else:
            logger.error("❌ Input validation failed")
            return False
        
        # Test with empty data
        empty_data = np.array([])
        if not preprocessor.validate_input(empty_data):
            logger.info("✅ Empty data handling working correctly")
        else:
            logger.error("❌ Empty data handling failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error handling test error: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🧪 Starting comprehensive system tests...")
    logger.info("=" * 60)
    
    tests = [
        ("Preprocessing Pipeline", test_preprocessing),
        ("Model Loading & Inference", test_model_loading),
        ("SHAP Explanations", test_shap_explanations),
        ("File Format Support", test_file_format_support),
        ("Error Handling", test_error_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        logger.info("🎉 ALL TESTS PASSED! System is ready for use.")
        return 0
    else:
        logger.error("⚠️  Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
