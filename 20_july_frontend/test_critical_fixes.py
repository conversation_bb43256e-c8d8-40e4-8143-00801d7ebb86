#!/usr/bin/env python3
"""
Comprehensive test for critical fixes in both CN/MCI/AD frontends
Tests: preprocessing methods, indexing bounds, class-specific heatmaps, brain alignment
"""

import sys
import numpy as np
import logging
from pathlib import Path
import tempfile
import nibabel as nib

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_preprocessing_methods():
    """Test that preprocessing methods exist and work correctly"""
    logger.info("Testing preprocessing methods...")
    
    try:
        # Test main frontend
        from cn_mci_ad_model import create_classifier
        classifier = create_classifier()
        
        # Test preprocessing method exists
        dummy_mri = np.random.randn(128, 128, 128) * 100 + 500
        tensor = classifier.preprocess_mri(dummy_mri)
        assert tensor.shape == (1, 1, 64, 64, 64), f"Expected (1,1,64,64,64), got {tensor.shape}"
        
        # Test demo frontend
        sys.path.insert(0, 'CN_MCI_AD_3Way_Demo')
        from cn_mci_ad_model import create_classifier as create_demo_classifier
        demo_classifier = create_demo_classifier()
        
        demo_tensor = demo_classifier.preprocess_mri(dummy_mri)
        assert demo_tensor.shape == (1, 1, 64, 64, 64), f"Expected (1,1,64,64,64), got {demo_tensor.shape}"
        
        logger.info("✅ Preprocessing methods test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Preprocessing methods test failed: {e}")
        return False

def test_class_specific_heatmaps():
    """Test that different classes generate different heatmaps"""
    logger.info("Testing class-specific heatmaps...")
    
    try:
        from cn_mci_ad_model import create_classifier
        from cn_mci_ad_shap import create_shap_explainer
        
        classifier = create_classifier()
        explainer = create_shap_explainer(classifier)
        
        # Create realistic MRI data
        dummy_mri = np.random.randn(128, 128, 128) * 100 + 500
        
        # Generate explanations for each class
        heatmaps = {}
        for class_idx, class_name in enumerate(classifier.class_names):
            saliency = explainer._gradient_explanation(dummy_mri, target_class=class_idx)
            heatmaps[class_name] = saliency
        
        # Verify heatmaps are different for different classes
        cn_heatmap = heatmaps['CN']
        mci_heatmap = heatmaps['MCI']
        ad_heatmap = heatmaps['AD']
        
        # Check that heatmaps are not identical
        cn_mci_diff = np.mean(np.abs(cn_heatmap - mci_heatmap))
        cn_ad_diff = np.mean(np.abs(cn_heatmap - ad_heatmap))
        mci_ad_diff = np.mean(np.abs(mci_heatmap - ad_heatmap))
        
        logger.info(f"Heatmap differences: CN-MCI={cn_mci_diff:.6f}, CN-AD={cn_ad_diff:.6f}, MCI-AD={mci_ad_diff:.6f}")
        
        # At least one pair should be significantly different
        min_difference = 0.001  # Minimum expected difference
        assert max(cn_mci_diff, cn_ad_diff, mci_ad_diff) > min_difference, "Heatmaps are too similar across classes"
        
        logger.info("✅ Class-specific heatmaps test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Class-specific heatmaps test failed: {e}")
        return False

def test_indexing_bounds():
    """Test that indexing doesn't go out of bounds"""
    logger.info("Testing indexing bounds...")
    
    try:
        from cn_mci_ad_shap import create_shap_explainer
        from cn_mci_ad_model import create_classifier
        
        classifier = create_classifier()
        explainer = create_shap_explainer(classifier)
        
        # Test with various MRI sizes
        test_sizes = [(64, 64, 64), (128, 128, 128), (192, 240, 256), (256, 256, 256)]
        
        for size in test_sizes:
            logger.info(f"Testing size {size}")
            dummy_mri = np.random.randn(*size) * 100 + 500
            
            # Test slice visualization
            saliency = explainer._gradient_explanation(dummy_mri, target_class=0)
            visualizations = explainer.generate_slice_visualizations(dummy_mri, saliency)
            
            # Verify all views are present
            for view in ['axial', 'sagittal', 'coronal']:
                assert view in visualizations, f"Missing {view} view"
                assert 'mri' in visualizations[view], f"Missing MRI data in {view}"
                assert 'heatmap' in visualizations[view], f"Missing heatmap in {view}"
                assert 'mask' in visualizations[view], f"Missing mask in {view}"
        
        logger.info("✅ Indexing bounds test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Indexing bounds test failed: {e}")
        return False

def test_brain_alignment():
    """Test that heatmaps are properly aligned with brain anatomy"""
    logger.info("Testing brain alignment...")
    
    try:
        from cn_mci_ad_shap import create_shap_explainer
        from cn_mci_ad_model import create_classifier
        
        classifier = create_classifier()
        explainer = create_shap_explainer(classifier)
        
        # Create MRI data with clear brain structure
        mri_data = np.zeros((128, 128, 128))
        
        # Create a brain-like ellipsoid in the center
        center = (64, 64, 64)
        for i in range(128):
            for j in range(128):
                for k in range(128):
                    # Ellipsoid equation
                    if ((i - center[0])/50)**2 + ((j - center[1])/50)**2 + ((k - center[2])/50)**2 < 1:
                        mri_data[i, j, k] = np.random.randn() * 50 + 1000
        
        # Test brain mask creation
        if hasattr(explainer, '_create_brain_mask'):
            brain_mask = explainer._create_brain_mask(mri_data)
        else:
            # Fallback test - create simple mask
            brain_mask = (mri_data > np.percentile(mri_data, 10)).astype(np.float32)
        
        # Verify brain mask is reasonable
        brain_volume = np.sum(brain_mask) / brain_mask.size
        assert 0.05 < brain_volume < 0.8, f"Brain volume {brain_volume:.2%} seems unrealistic"
        
        # Test gradient explanation with brain alignment
        saliency = explainer._gradient_explanation(mri_data, target_class=0)
        
        # Verify saliency is mostly within brain regions
        saliency_in_brain = np.sum(saliency[brain_mask > 0])
        saliency_outside_brain = np.sum(saliency[brain_mask == 0])
        
        if saliency_in_brain + saliency_outside_brain > 0:
            outside_ratio = saliency_outside_brain / (saliency_in_brain + saliency_outside_brain)
            logger.info(f"Saliency outside brain: {outside_ratio:.1%}")
            assert outside_ratio < 0.2, f"Too much saliency outside brain: {outside_ratio:.1%}"
        
        logger.info("✅ Brain alignment test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Brain alignment test failed: {e}")
        return False

def test_demo_frontend_compatibility():
    """Test that demo frontend works without errors"""
    logger.info("Testing demo frontend compatibility...")
    
    try:
        sys.path.insert(0, 'CN_MCI_AD_3Way_Demo')
        from cn_mci_ad_model import create_classifier
        from cn_mci_ad_shap import create_shap_explainer
        
        classifier = create_classifier()
        explainer = create_shap_explainer(classifier)
        
        # Test prediction interface
        dummy_mri = np.random.randn(128, 128, 128) * 100 + 500
        results = classifier.predict(dummy_mri)
        
        # Test interface compatibility
        predictions = results['probabilities']
        confidence = results['confidence']
        confidence_level = f"{confidence:.1%}"
        
        assert isinstance(predictions, dict)
        assert isinstance(confidence, float)
        assert isinstance(confidence_level, str)
        
        # Test SHAP explanation
        explanation = explainer.generate_explanations(dummy_mri)
        assert isinstance(explanation, dict)
        
        logger.info("✅ Demo frontend compatibility test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Demo frontend compatibility test failed: {e}")
        return False

def main():
    """Run all critical tests"""
    logger.info("🧪 Starting critical fixes validation...")
    logger.info("=" * 60)
    
    tests = [
        ("Preprocessing Methods", test_preprocessing_methods),
        ("Class-Specific Heatmaps", test_class_specific_heatmaps),
        ("Indexing Bounds", test_indexing_bounds),
        ("Brain Alignment", test_brain_alignment),
        ("Demo Frontend Compatibility", test_demo_frontend_compatibility)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 CRITICAL FIXES TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        logger.info("🎉 ALL CRITICAL FIXES VALIDATED! Both frontends are ready.")
        return 0
    else:
        logger.error("⚠️  Some critical issues remain. Please check the failures above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
