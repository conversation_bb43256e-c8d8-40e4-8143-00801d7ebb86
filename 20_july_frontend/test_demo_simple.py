#!/usr/bin/env python3
"""
Simple test for demo frontend to isolate the issue
"""

import sys
import numpy as np
import logging
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_demo_simple():
    """Simple test for demo frontend"""
    logger.info("Testing demo frontend...")
    
    try:
        # Change to demo directory
        original_cwd = os.getcwd()
        demo_path = os.path.join(original_cwd, 'CN_MCI_AD_3Way_Demo')
        os.chdir(demo_path)
        
        # Add demo path to Python path
        if demo_path not in sys.path:
            sys.path.insert(0, demo_path)
        
        # Import demo modules
        from cn_mci_ad_model import create_classifier
        from cn_mci_ad_shap import create_shap_explainer
        
        logger.info("Creating demo classifier...")
        classifier = create_classifier()
        
        logger.info(f"Classifier type: {type(classifier)}")
        logger.info(f"Classifier has preprocess_mri: {hasattr(classifier, 'preprocess_mri')}")
        
        if hasattr(classifier, 'preprocess_mri'):
            logger.info("✅ Classifier has preprocess_mri method")
        else:
            logger.error("❌ Classifier missing preprocess_mri method")
            return False
        
        logger.info("Creating demo explainer...")
        explainer = create_shap_explainer(classifier)
        
        logger.info(f"Explainer type: {type(explainer)}")
        logger.info(f"Explainer classifier type: {type(explainer.classifier)}")
        logger.info(f"Explainer classifier has preprocess_mri: {hasattr(explainer.classifier, 'preprocess_mri')}")
        
        # Test with small MRI data
        mri_data = np.random.randn(64, 64, 64) * 100 + 500
        
        logger.info("Testing classifier prediction...")
        results = classifier.predict(mri_data)
        logger.info(f"Prediction successful: {type(results)}")
        
        logger.info("Testing explainer...")
        try:
            explanation = explainer.generate_explanations(mri_data)
            logger.info(f"Explanation successful: {type(explanation)}")
            logger.info("✅ Demo frontend test passed!")
            return True
        except Exception as e:
            logger.error(f"Explainer error: {e}")
            logger.error(f"Explainer classifier type at error: {type(explainer.classifier)}")
            
            # Try to debug the issue
            if hasattr(explainer, 'classifier'):
                if hasattr(explainer.classifier, 'preprocess_mri'):
                    logger.info("Classifier has preprocess_mri method")
                else:
                    logger.error("Classifier missing preprocess_mri method")
                    # Check what methods it does have
                    methods = [method for method in dir(explainer.classifier) if not method.startswith('_')]
                    logger.info(f"Available methods: {methods}")
            
            return False
        
    except Exception as e:
        logger.error(f"Demo test failed: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)

if __name__ == "__main__":
    success = test_demo_simple()
    if success:
        print("✅ Demo frontend works correctly!")
        sys.exit(0)
    else:
        print("❌ Demo frontend has issues!")
        sys.exit(1)
