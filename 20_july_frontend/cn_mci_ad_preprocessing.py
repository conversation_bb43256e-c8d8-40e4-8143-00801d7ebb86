"""
Enhanced MRI Preprocessing Pipeline for CN/MCI/AD 3-Category Classification
Robust preprocessing with comprehensive error handling and format support
"""

import os
import numpy as np
import nibabel as nib
import tempfile
from pathlib import Path
from typing import Union, Tuple, Optional, Dict, Any
import logging
from scipy.ndimage import zoom, gaussian_filter
import warnings

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore', category=UserWarning)

class CNMCIADPreprocessor:
    """
    Enhanced preprocessing pipeline for CN/MCI/AD classification with robust error handling
    """

    def __init__(self, target_size: Tuple[int, int, int] = (64, 64, 64)):
        """
        Initialize preprocessor

        Args:
            target_size: Target size for resizing (default: 64x64x64)
        """
        self.target_size = target_size
        self.supported_formats = ['.nii', '.nii.gz', '.npy']
        logger.info(f"Initialized enhanced CN/MCI/AD preprocessor with target size: {target_size}")
        logger.info(f"Supported formats: {self.supported_formats}")

    def validate_input(self, data: np.ndarray) -> bool:
        """
        Validate input MRI data

        Args:
            data: Input MRI data

        Returns:
            True if valid, False otherwise
        """
        try:
            if data is None:
                logger.error("Input data is None")
                return False

            if not isinstance(data, np.ndarray):
                logger.error(f"Input data must be numpy array, got {type(data)}")
                return False

            if data.size == 0:
                logger.error("Input data is empty")
                return False

            if data.ndim < 3 or data.ndim > 4:
                logger.error(f"Input data must be 3D or 4D, got {data.ndim}D")
                return False

            if np.all(data == 0):
                logger.warning("Input data contains all zeros")
                return False

            if not np.isfinite(data).all():
                logger.error("Input data contains non-finite values")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating input: {e}")
            return False
    
    def normalize_intensity(self, data: np.ndarray) -> np.ndarray:
        """
        Enhanced intensity normalization with robust handling

        Args:
            data: Input MRI data

        Returns:
            Normalized data
        """
        try:
            # Create a copy to avoid modifying original data
            data_copy = data.copy()

            # Remove outliers (clip to 1st and 99th percentiles)
            p1, p99 = np.percentile(data_copy, [1, 99])
            data_copy = np.clip(data_copy, p1, p99)

            # Z-score normalization with robust handling
            mean = np.mean(data_copy)
            std = np.std(data_copy)

            if std > 1e-8:  # More robust threshold
                normalized = (data_copy - mean) / std
            else:
                logger.warning("Standard deviation too small, using min-max normalization")
                data_min, data_max = np.min(data_copy), np.max(data_copy)
                if data_max > data_min:
                    normalized = (data_copy - data_min) / (data_max - data_min)
                    # Scale to [-1, 1] range
                    normalized = 2 * normalized - 1
                else:
                    normalized = np.zeros_like(data_copy)

            # Ensure finite values
            normalized = np.nan_to_num(normalized, nan=0.0, posinf=1.0, neginf=-1.0)

            logger.debug(f"Normalization: mean={np.mean(normalized):.3f}, std={np.std(normalized):.3f}")
            return normalized

        except Exception as e:
            logger.error(f"Error in intensity normalization: {e}")
            # Return original data if normalization fails
            return data
    
    def resize_volume(self, data: np.ndarray, target_size: Tuple[int, int, int]) -> np.ndarray:
        """
        Enhanced volume resizing with error handling

        Args:
            data: Input volume
            target_size: Target dimensions

        Returns:
            Resized volume
        """
        try:
            current_size = data.shape

            # Check if resizing is needed
            if current_size == target_size:
                logger.debug("No resizing needed")
                return data

            # Calculate zoom factors
            zoom_factors = [target_size[i] / current_size[i] for i in range(3)]

            logger.info(f"Resizing from {current_size} to {target_size}")
            logger.debug(f"Zoom factors: {zoom_factors}")

            # Use higher order interpolation for better quality
            resized = zoom(data, zoom_factors, order=3, mode='nearest', prefilter=True)

            # Verify output shape
            if resized.shape != target_size:
                logger.warning(f"Resize result shape {resized.shape} != target {target_size}")
                # Force exact size if needed
                resized = resized[:target_size[0], :target_size[1], :target_size[2]]
                if resized.shape != target_size:
                    # Pad if too small
                    pad_width = [(0, max(0, target_size[i] - resized.shape[i])) for i in range(3)]
                    resized = np.pad(resized, pad_width, mode='constant', constant_values=0)

            return resized

        except Exception as e:
            logger.error(f"Error resizing volume: {e}")
            # Return original data if resize fails
            return data
    
    def load_mri_file(self, input_path: Union[str, Path]) -> Optional[np.ndarray]:
        """
        Load MRI file from various formats

        Args:
            input_path: Path to input file

        Returns:
            Loaded MRI data or None if failed
        """
        try:
            input_path = Path(input_path)

            if not input_path.exists():
                logger.error(f"File does not exist: {input_path}")
                return None

            # Check file format
            if input_path.suffix.lower() in ['.nii', '.gz']:
                # Load NIfTI file
                img = nib.load(input_path)
                data = img.get_fdata()
                logger.info(f"Loaded NIfTI file: {input_path.name}")

            elif input_path.suffix.lower() == '.npy':
                # Load numpy file
                data = np.load(input_path, allow_pickle=True)
                logger.info(f"Loaded numpy file: {input_path.name}")

            else:
                logger.error(f"Unsupported file format: {input_path.suffix}")
                return None

            return data

        except Exception as e:
            logger.error(f"Error loading file {input_path}: {e}")
            return None

    def preprocess_scan(self, input_path: Union[str, Path]) -> Optional[np.ndarray]:
        """
        Enhanced preprocessing of a single MRI scan

        Args:
            input_path: Path to input file

        Returns:
            Preprocessed scan as numpy array, or None if failed
        """
        try:
            input_path = Path(input_path)
            logger.info(f"Preprocessing scan: {input_path.name}")

            # Load the file
            data = self.load_mri_file(input_path)
            if data is None:
                return None

            logger.info(f"Original shape: {data.shape}")
            logger.info(f"Original intensity range: [{np.min(data):.3f}, {np.max(data):.3f}]")

            # Validate input
            if not self.validate_input(data):
                logger.error("Input validation failed")
                return None

            # Ensure 3D
            if data.ndim == 4:
                # Take first volume if 4D
                data = data[:, :, :, 0]
                logger.info("Converted 4D to 3D by taking first volume")
            elif data.ndim > 4:
                logger.error(f"Cannot handle {data.ndim}D data")
                return None

            # Resize to target size
            if data.shape != self.target_size:
                data = self.resize_volume(data, self.target_size)

            # Normalize intensity
            data = self.normalize_intensity(data)

            # Apply light smoothing to reduce noise
            data = gaussian_filter(data, sigma=0.5)

            logger.info(f"Final shape: {data.shape}")
            logger.info(f"Final intensity range: [{np.min(data):.3f}, {np.max(data):.3f}]")

            return data

        except Exception as e:
            logger.error(f"Error preprocessing {input_path}: {e}")
            return None
    
    def preprocess_from_bytes(self, file_bytes: bytes, filename: str) -> Optional[np.ndarray]:
        """
        Preprocess MRI scan from bytes (for Streamlit file upload)
        
        Args:
            file_bytes: File content as bytes
            filename: Original filename
            
        Returns:
            Preprocessed scan as numpy array, or None if failed
        """
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.nii') as tmp_file:
                tmp_file.write(file_bytes)
                tmp_file.flush()
                
                # Process the temporary file
                result = self.preprocess_scan(tmp_file.name)
                
                # Clean up
                os.unlink(tmp_file.name)
                
                return result
                
        except Exception as e:
            logger.error(f"Error preprocessing {filename}: {e}")
            return None
    
    def batch_preprocess(self, input_dir: Union[str, Path], 
                        output_dir: Union[str, Path]) -> int:
        """
        Preprocess multiple scans in a directory
        
        Args:
            input_dir: Directory containing input NIfTI files
            output_dir: Directory to save preprocessed files
            
        Returns:
            Number of successfully processed files
        """
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Find all NIfTI files
        nii_files = list(input_dir.glob("*.nii")) + list(input_dir.glob("*.nii.gz"))
        
        logger.info(f"Found {len(nii_files)} NIfTI files to process")
        
        processed_count = 0
        
        for nii_file in nii_files:
            try:
                # Preprocess
                processed_data = self.preprocess_scan(nii_file)
                
                if processed_data is not None:
                    # Save as numpy array
                    output_path = output_dir / f"{nii_file.stem}_preprocessed.npy"
                    np.save(output_path, processed_data)
                    logger.info(f"Saved: {output_path}")
                    processed_count += 1
                else:
                    logger.warning(f"Failed to process: {nii_file}")
                    
            except Exception as e:
                logger.error(f"Error processing {nii_file}: {e}")
        
        logger.info(f"Successfully processed {processed_count}/{len(nii_files)} files")
        return processed_count
    
    def get_preprocessing_info(self) -> dict:
        """
        Get information about preprocessing parameters
        
        Returns:
            Dictionary with preprocessing information
        """
        return {
            'target_size': self.target_size,
            'normalization': 'Z-score normalization',
            'outlier_handling': 'Clip to 1st-99th percentiles',
            'resampling': 'Linear interpolation (order=1)',
            'model_compatibility': 'Memory-Efficient CNN for CN/MCI/AD'
        }


def create_preprocessor(target_size: Tuple[int, int, int] = (64, 64, 64)) -> CNMCIADPreprocessor:
    """
    Factory function to create preprocessor
    
    Args:
        target_size: Target size for preprocessing
        
    Returns:
        Initialized preprocessor
    """
    return CNMCIADPreprocessor(target_size)


# Test function
def test_preprocessor():
    """Test the preprocessor with a dummy volume"""
    logger.info("Testing CN/MCI/AD preprocessor...")
    
    # Create dummy data
    dummy_data = np.random.randn(128, 128, 128) * 100 + 500
    
    # Save as temporary NIfTI
    with tempfile.NamedTemporaryFile(suffix='.nii', delete=False) as tmp_file:
        img = nib.Nifti1Image(dummy_data, np.eye(4))
        nib.save(img, tmp_file.name)
        
        # Test preprocessing
        preprocessor = create_preprocessor()
        result = preprocessor.preprocess_scan(tmp_file.name)
        
        # Clean up
        os.unlink(tmp_file.name)
        
        if result is not None:
            logger.info(f"✅ Test passed! Output shape: {result.shape}")
            logger.info(f"   Intensity range: [{np.min(result):.3f}, {np.max(result):.3f}]")
            return True
        else:
            logger.error("❌ Test failed!")
            return False


if __name__ == "__main__":
    test_preprocessor()
