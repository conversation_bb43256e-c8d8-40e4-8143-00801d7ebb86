"""
Demetify - CN/MCI/AD 3-Category Classification Frontend
Professional interface for comprehensive dementia assessment
"""

import streamlit as st
import numpy as np
import nibabel as nib
import matplotlib.pyplot as plt
from pathlib import Path
import tempfile
import os
import logging
import time
from typing import Optional, Dict, Any, Tuple
import time

# Import nilearn for proper MRI visualization
try:
    from nilearn import plotting, image
    from nilearn.plotting import plot_anat, plot_stat_map
    NILEARN_AVAILABLE = True
except ImportError:
    NILEARN_AVAILABLE = False
    st.warning("Nilearn not available. Install with: pip install nilearn")

# Import our custom components
from cn_mci_ad_model import create_classifier
from cn_mci_ad_shap import create_shap_explainer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Demetify - CN/MCI/AD Assessment",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for professional styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #2d5aa0;
        text-align: center;
        margin-bottom: 1rem;
    }
    .sub-header {
        font-size: 1.2rem;
        color: #666;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
    }
    .result-card {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    .confidence-high { color: #28a745; font-weight: bold; }
    .confidence-medium { color: #ffc107; font-weight: bold; }
    .confidence-low { color: #dc3545; font-weight: bold; }
    .class-cn { background: linear-gradient(90deg, #28a745, #20c997); }
    .class-mci { background: linear-gradient(90deg, #ffc107, #fd7e14); }
    .class-ad { background: linear-gradient(90deg, #dc3545, #e83e8c); }
</style>
""", unsafe_allow_html=True)

def display_header():
    """Display the main header"""
    st.markdown('<div class="main-header">🧠 Demetify - CN/MCI/AD Assessment</div>', unsafe_allow_html=True)
    st.markdown('<div class="sub-header">Advanced 3-Category Dementia Classification System</div>', unsafe_allow_html=True)
    st.markdown("---")

@st.cache_resource
def load_models():
    """Load and cache the models with enhanced error handling"""
    try:
        with st.spinner("Loading AI models..."):
            # Use the enhanced factory functions
            classifier = create_classifier()
            explainer = create_shap_explainer(classifier)

            # Display model status
            if classifier.model_loaded:
                st.success("✅ Trained model loaded successfully (300 epochs)")
            else:
                st.warning("⚠️ Using demo mode with random weights")

        return classifier, explainer
    except Exception as e:
        st.error(f"Error loading models: {e}")
        logger.error(f"Model loading error: {e}")
        return None, None

def display_mri_slice(mri_data: np.ndarray, slice_idx: int, axis: int, title: str):
    """Display a single MRI slice"""
    fig, ax = plt.subplots(1, 1, figsize=(6, 6))
    
    if axis == 0:  # Sagittal
        slice_data = mri_data[slice_idx, :, :]
    elif axis == 1:  # Coronal
        slice_data = mri_data[:, slice_idx, :]
    else:  # Axial
        slice_data = mri_data[:, :, slice_idx]
    
    ax.imshow(slice_data, cmap='gray', origin='lower')
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.axis('off')
    
    return fig

def display_prediction_results(predictions: Dict[str, float], confidence_level: str):
    """Display prediction results with professional styling"""
    st.markdown("### 📊 Classification Results")
    
    # Create three columns for the classes
    col1, col2, col3 = st.columns(3)
    
    classes = ['CN', 'MCI', 'AD']
    class_names = {
        'CN': 'Cognitively Normal',
        'MCI': 'Mild Cognitive Impairment', 
        'AD': "Alzheimer's Disease"
    }
    class_colors = ['#28a745', '#ffc107', '#dc3545']
    
    for i, (col, class_key) in enumerate(zip([col1, col2, col3], classes)):
        with col:
            prob = predictions.get(class_key, 0.0)
            st.markdown(f"""
            <div class="result-card">
                <h4 style="color: {class_colors[i]}; margin-bottom: 0.5rem;">{class_key}</h4>
                <p style="font-size: 0.9rem; margin-bottom: 1rem;">{class_names[class_key]}</p>
                <div style="background: {class_colors[i]}; height: 20px; border-radius: 10px; width: {prob*100}%; margin-bottom: 0.5rem;"></div>
                <p style="font-size: 1.2rem; font-weight: bold; margin: 0;">{prob:.1%}</p>
            </div>
            """, unsafe_allow_html=True)
    
    # Display confidence level
    confidence_class = f"confidence-{confidence_level.lower()}"
    st.markdown(f"""
    <div class="result-card" style="text-align: center;">
        <h4>Overall Confidence</h4>
        <p class="{confidence_class}" style="font-size: 1.5rem;">{confidence_level.upper()}</p>
    </div>
    """, unsafe_allow_html=True)

def display_interpretability_maps(shap_results: Dict, mri_data: np.ndarray):
    """Display SHAP interpretability heatmaps"""
    st.markdown("### 🔍 Interpretability Analysis")
    
    # Create tabs for different classes
    tab1, tab2, tab3 = st.tabs(["CN Explanation", "MCI Explanation", "AD Explanation"])
    
    classes = ['CN', 'MCI', 'AD']
    
    for tab, class_key in zip([tab1, tab2, tab3], classes):
        with tab:
            if f'{class_key.lower()}_shap_values' in shap_results:
                shap_values = shap_results[f'{class_key.lower()}_shap_values']
                
                st.markdown(f"#### Brain regions important for {class_key} classification:")
                
                # Display three anatomical views
                col1, col2, col3 = st.columns(3)
                
                # Get middle slices with dimension checking
                logger.info(f"MRI data shape: {mri_data.shape}, SHAP values shape: {shap_values.shape}")

                # Calculate slice indices for MRI data
                mid_sag_mri = mri_data.shape[0] // 2
                mid_cor_mri = mri_data.shape[1] // 2
                mid_ax_mri = mri_data.shape[2] // 2

                # Calculate corresponding slice indices for SHAP values
                mid_sag_shap = shap_values.shape[0] // 2
                mid_cor_shap = shap_values.shape[1] // 2
                mid_ax_shap = shap_values.shape[2] // 2
                
                with col1:
                    fig = display_mri_slice(mri_data, mid_sag_mri, 0, "Sagittal View")
                    st.pyplot(fig)
                    plt.close()

                with col2:
                    fig = display_mri_slice(mri_data, mid_cor_mri, 1, "Coronal View")
                    st.pyplot(fig)
                    plt.close()

                with col3:
                    fig = display_mri_slice(mri_data, mid_ax_mri, 2, "Axial View")
                    st.pyplot(fig)
                    plt.close()
                
                # Display enhanced heatmap overlay
                st.markdown("**Importance Heatmap with Brain Overlay:**")

                # Create overlay visualization with proper dimension handling
                fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))

                # Original brain slice (use MRI indices)
                brain_slice = mri_data[:, :, mid_ax_mri]
                ax1.imshow(brain_slice, cmap='gray', origin='lower')
                ax1.set_title(f"Original Brain (Axial)", fontweight='bold')
                ax1.axis('off')

                # Heatmap only (use SHAP indices)
                heatmap_slice = shap_values[:, :, mid_ax_shap]
                im2 = ax2.imshow(heatmap_slice, cmap='hot', origin='lower')
                ax2.set_title(f"{class_key} Importance Map", fontweight='bold')
                ax2.axis('off')
                plt.colorbar(im2, ax=ax2, label='Importance Score', shrink=0.8)

                # Overlay
                ax3.imshow(brain_slice, cmap='gray', origin='lower', alpha=0.7)
                im3 = ax3.imshow(heatmap_slice, cmap='hot', origin='lower', alpha=0.6)
                ax3.set_title(f"Brain + {class_key} Heatmap", fontweight='bold')
                ax3.axis('off')

                plt.tight_layout()
                st.pyplot(fig)
                plt.close()

                # Additional slice views
                st.markdown("**Multi-planar Views:**")
                fig2, axes = plt.subplots(2, 3, figsize=(15, 10))

                # Sagittal views (use appropriate indices)
                brain_sag = mri_data[mid_sag_mri, :, :]
                heatmap_sag = shap_values[mid_sag_shap, :, :]

                axes[0, 0].imshow(brain_sag, cmap='gray', origin='lower')
                axes[0, 0].set_title('Sagittal - Brain', fontweight='bold')
                axes[0, 0].axis('off')

                axes[1, 0].imshow(brain_sag, cmap='gray', origin='lower', alpha=0.7)
                axes[1, 0].imshow(heatmap_sag, cmap='hot', origin='lower', alpha=0.6)
                axes[1, 0].set_title('Sagittal - Overlay', fontweight='bold')
                axes[1, 0].axis('off')

                # Coronal views (use appropriate indices)
                brain_cor = mri_data[:, mid_cor_mri, :]
                heatmap_cor = shap_values[:, mid_cor_shap, :]

                axes[0, 1].imshow(brain_cor, cmap='gray', origin='lower')
                axes[0, 1].set_title('Coronal - Brain', fontweight='bold')
                axes[0, 1].axis('off')

                axes[1, 1].imshow(brain_cor, cmap='gray', origin='lower', alpha=0.7)
                axes[1, 1].imshow(heatmap_cor, cmap='hot', origin='lower', alpha=0.6)
                axes[1, 1].set_title('Coronal - Overlay', fontweight='bold')
                axes[1, 1].axis('off')

                # Axial views (repeated for consistency)
                axes[0, 2].imshow(brain_slice, cmap='gray', origin='lower')
                axes[0, 2].set_title('Axial - Brain', fontweight='bold')
                axes[0, 2].axis('off')

                axes[1, 2].imshow(brain_slice, cmap='gray', origin='lower', alpha=0.7)
                axes[1, 2].imshow(heatmap_slice, cmap='hot', origin='lower', alpha=0.6)
                axes[1, 2].set_title('Axial - Overlay', fontweight='bold')
                axes[1, 2].axis('off')

                plt.tight_layout()
                st.pyplot(fig2)
                plt.close()
            else:
                st.info(f"SHAP analysis for {class_key} not available")

def main():
    """Main application"""
    
    display_header()
    
    # Initialize session state for proper state management
    if 'demo_current_file_id' not in st.session_state:
        st.session_state.demo_current_file_id = None
    if 'demo_mri_data' not in st.session_state:
        st.session_state.demo_mri_data = None
    if 'demo_analysis_results' not in st.session_state:
        st.session_state.demo_analysis_results = None
    if 'demo_explanation_data' not in st.session_state:
        st.session_state.demo_explanation_data = None
    if 'demo_file_processed' not in st.session_state:
        st.session_state.demo_file_processed = False

    # Load models
    classifier, explainer = load_models()

    if classifier is None:
        st.error("Failed to load models. Please check the model files.")
        st.info("**Troubleshooting:**")
        st.info("1. Ensure all model files are present")
        st.info("2. Check that dependencies are installed: `pip install -r requirements.txt`")
        st.info("3. Verify Python version compatibility")
        return

    # File upload section
    st.markdown("### 📁 Upload MRI Scan")
    uploaded_file = st.file_uploader(
        "Choose a T1-weighted MRI file (NIfTI format)",
        type=['nii', 'nii.gz'],
        help="Upload a T1-weighted structural MRI scan in NIfTI format",
        key="demo_mri_uploader"
    )

    # Check if a new file has been uploaded
    current_file_id = None
    if uploaded_file is not None:
        current_file_id = f"{uploaded_file.name}_{uploaded_file.size}_{hash(uploaded_file.getvalue())}"

        # Reset state if new file is uploaded
        if current_file_id != st.session_state.demo_current_file_id:
            st.session_state.demo_current_file_id = current_file_id
            st.session_state.demo_mri_data = None
            st.session_state.demo_analysis_results = None
            st.session_state.demo_explanation_data = None
            st.session_state.demo_file_processed = False
            logger.info(f"New demo file detected: {uploaded_file.name}")
    
    if uploaded_file is not None:
        # Only process if not already done for this file
        if not st.session_state.demo_file_processed or st.session_state.demo_mri_data is None:
            try:
                # Create progress bar
                progress_bar = st.progress(0)
                status_text = st.empty()

                # Save uploaded file temporarily with proper extension handling
                status_text.text("📥 Processing uploaded file...")
                progress_bar.progress(10)

                # Determine proper file extension
                filename = uploaded_file.name.lower()
                if filename.endswith('.nii.gz'):
                    suffix = '.nii.gz'
                elif filename.endswith('.nii'):
                    suffix = '.nii'
                else:
                    suffix = '.nii.gz'  # Default fallback

                with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
                    tmp_file.write(uploaded_file.getvalue())
                    tmp_path = tmp_file.name

                # Load and preprocess MRI data
                status_text.text("🧠 Loading MRI data...")
                progress_bar.progress(30)

                img = nib.load(tmp_path)
                st.session_state.demo_mri_data = img.get_fdata()
                logger.info(f"Demo: Loaded MRI data with shape: {st.session_state.demo_mri_data.shape}")

                # Clean up temporary file
                os.unlink(tmp_path)

            except Exception as e:
                st.error(f"Error loading MRI data: {e}")
                logger.error(f"Demo MRI loading error: {e}")
                return

        # Use cached MRI data
        mri_data = st.session_state.demo_mri_data
        if mri_data is not None:

            # Display basic info
            st.markdown("### 📋 Scan Information")
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Dimensions", f"{mri_data.shape[0]}×{mri_data.shape[1]}×{mri_data.shape[2]}")
            with col2:
                st.metric("Voxel Size", "1.00mm")  # Default since img may not be available
            with col3:
                st.metric("Data Type", str(mri_data.dtype))

            # Run analysis only if not already done for this file
            if st.session_state.demo_analysis_results is None or not st.session_state.demo_file_processed:
                # Create progress indicators
                progress_bar = st.progress(0)
                status_text = st.empty()

                # Perform classification
                status_text.text("🤖 Running AI analysis...")
                progress_bar.progress(60)

                st.session_state.demo_analysis_results = classifier.predict(mri_data)
                logger.info(f"Demo: Analysis completed for {uploaded_file.name}")

                # Extract predictions and confidence for compatibility
                predictions = st.session_state.demo_analysis_results['probabilities']
                confidence = st.session_state.demo_analysis_results['confidence']
                confidence_level = f"{confidence:.1%}"

                # Display results
                progress_bar.progress(80)

                # Generate interpretability maps with proper error handling
                if explainer is not None:
                    status_text.text("🔍 Generating interpretability maps...")
                    progress_bar.progress(90)

                    try:
                        st.session_state.demo_explanation_data = explainer.generate_explanations(mri_data)
                        logger.info("Demo: Interpretability maps generated successfully")
                    except Exception as e:
                        st.warning(f"Could not generate interpretability maps: {e}")
                        st.info("Classification results are still valid.")
                        logger.error(f"Demo interpretability error: {e}")
                        st.session_state.demo_explanation_data = None

                # Mark as processed
                st.session_state.demo_file_processed = True

                # Clear progress indicators
                progress_bar.progress(100)
                status_text.text("✅ Analysis complete!")
                time.sleep(1)  # Brief pause to show completion
                status_text.empty()
                progress_bar.empty()

            # Display results from session state
            if st.session_state.demo_analysis_results is not None:
                predictions = st.session_state.demo_analysis_results['probabilities']
                confidence = st.session_state.demo_analysis_results['confidence']
                confidence_level = f"{confidence:.1%}"

                display_prediction_results(predictions, confidence_level)

                # Display interpretability maps if available
                if st.session_state.demo_explanation_data is not None:
                    display_interpretability_maps(st.session_state.demo_explanation_data, mri_data)
            else:
                st.warning("Analysis results not available. Please try uploading the file again.")
        else:
            st.warning("MRI data not available. Please try uploading the file again.")
    
    # Information section
    with st.expander("ℹ️ About This System"):
        st.markdown("""
        **Demetify CN/MCI/AD Classification System**
        
        This AI system analyzes T1-weighted MRI scans to classify brain health into three categories:
        
        - **CN (Cognitively Normal)**: No signs of cognitive impairment
        - **MCI (Mild Cognitive Impairment)**: Early-stage cognitive decline
        - **AD (Alzheimer's Disease)**: Advanced dementia pathology
        
        **Technical Details:**
        - Model: Memory-Efficient 3D CNN
        - Input: T1-weighted MRI scans (NIfTI format)
        - Processing: Automated preprocessing and normalization
        - Interpretability: SHAP-based explanations
        - Training Data: NACC + ADNI datasets
        
        **Confidence Levels:**
        - **HIGH**: >70% confidence in prediction
        - **MEDIUM**: 50-70% confidence
        - **LOW**: <50% confidence
        """)

if __name__ == "__main__":
    main()
