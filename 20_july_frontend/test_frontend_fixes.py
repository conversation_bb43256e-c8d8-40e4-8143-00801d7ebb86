#!/usr/bin/env python3
"""
Comprehensive test script for both frontends to ensure no errors
Tests all components: model loading, prediction, SHAP generation, and brain alignment
"""

import sys
import numpy as np
import logging
from pathlib import Path
import tempfile
import nibabel as nib

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_main_frontend():
    """Test the main frontend (port 8503) components"""
    logger.info("Testing main frontend components...")
    
    try:
        from cn_mci_ad_model import create_classifier
        from cn_mci_ad_shap import create_shap_explainer
        
        # Create components
        classifier = create_classifier()
        explainer = create_shap_explainer(classifier)
        
        # Test with dummy MRI data (realistic size)
        dummy_mri = np.random.randn(192, 240, 256) * 100 + 500
        
        # Test prediction
        results = classifier.predict(dummy_mri)
        
        # Verify results structure
        assert 'predicted_class' in results
        assert 'probabilities' in results
        assert 'confidence' in results
        
        # Test SHAP explanation
        explanation = explainer.generate_full_explanation(dummy_mri)
        
        # Verify explanation structure
        for class_name in classifier.class_names:
            viz_key = f'{class_name.lower()}_visualizations'
            assert viz_key in explanation
            
            viz = explanation[viz_key]
            for view in ['axial', 'sagittal', 'coronal']:
                assert view in viz
                assert 'mri' in viz[view]
                assert 'heatmap' in viz[view]
                assert 'mask' in viz[view]
        
        logger.info("✅ Main frontend test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Main frontend test failed: {e}")
        return False

def test_demo_frontend():
    """Test the demo frontend (port 8504) components"""
    logger.info("Testing demo frontend components...")
    
    try:
        import sys
        sys.path.insert(0, 'CN_MCI_AD_3Way_Demo')
        
        from cn_mci_ad_model import create_classifier
        from cn_mci_ad_shap import create_shap_explainer
        
        # Create components
        classifier = create_classifier()
        explainer = create_shap_explainer(classifier)
        
        # Test with dummy MRI data
        dummy_mri = np.random.randn(192, 240, 256) * 100 + 500
        
        # Test prediction (this was causing the unpacking error)
        results = classifier.predict(dummy_mri)
        
        # Test the fixed interface
        predictions = results['probabilities']
        confidence = results['confidence']
        confidence_level = f"{confidence:.1%}"
        
        # Verify the data types are correct
        assert isinstance(predictions, dict)
        assert isinstance(confidence, float)
        assert isinstance(confidence_level, str)
        
        # Test SHAP explanation
        explanation = explainer.generate_full_explanation(dummy_mri)
        
        logger.info("✅ Demo frontend test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Demo frontend test failed: {e}")
        return False

def test_brain_alignment():
    """Test that heatmaps are properly aligned with brain anatomy"""
    logger.info("Testing brain alignment...")
    
    try:
        from cn_mci_ad_shap import create_shap_explainer
        from cn_mci_ad_model import create_classifier
        
        classifier = create_classifier()
        explainer = create_shap_explainer(classifier)
        
        # Create realistic MRI data with brain-like structure
        mri_data = np.zeros((192, 240, 256))
        
        # Create a brain-like ellipsoid in the center
        center = (96, 120, 128)
        for i in range(192):
            for j in range(240):
                for k in range(256):
                    # Ellipsoid equation
                    if ((i - center[0])/80)**2 + ((j - center[1])/100)**2 + ((k - center[2])/100)**2 < 1:
                        mri_data[i, j, k] = np.random.randn() * 50 + 1000
        
        # Test brain mask creation
        brain_mask = explainer._create_brain_mask(mri_data)
        
        # Verify brain mask is reasonable
        brain_volume = np.sum(brain_mask) / brain_mask.size
        assert 0.1 < brain_volume < 0.8, f"Brain volume {brain_volume:.2%} seems unrealistic"
        
        # Test gradient explanation with brain alignment
        saliency = explainer._gradient_explanation(mri_data, target_class=0)
        
        # Verify saliency is only within brain regions
        saliency_outside_brain = np.sum(saliency[brain_mask == 0])
        saliency_inside_brain = np.sum(saliency[brain_mask > 0])
        
        if saliency_inside_brain > 0:
            outside_ratio = saliency_outside_brain / (saliency_inside_brain + saliency_outside_brain)
            logger.info(f"Saliency outside brain: {outside_ratio:.1%}")
            assert outside_ratio < 0.1, f"Too much saliency outside brain: {outside_ratio:.1%}"
        
        logger.info("✅ Brain alignment test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Brain alignment test failed: {e}")
        return False

def test_file_loading():
    """Test file loading with different formats"""
    logger.info("Testing file loading...")
    
    try:
        from cn_mci_ad_frontend import load_mri_data
        
        # Create dummy data
        dummy_data = np.random.randn(64, 64, 64) * 100 + 500
        
        # Test .nii file
        with tempfile.NamedTemporaryFile(suffix='.nii', delete=False) as tmp_file:
            img = nib.Nifti1Image(dummy_data, np.eye(4))
            nib.save(img, tmp_file.name)
            
            # Mock uploaded file
            class MockFile:
                def __init__(self, path):
                    self.name = Path(path).name
                    with open(path, 'rb') as f:
                        self.data = f.read()
                def getvalue(self):
                    return self.data
            
            mock_file = MockFile(tmp_file.name)
            result = load_mri_data(mock_file)
            
            assert result is not None
            assert result.shape == dummy_data.shape
        
        logger.info("✅ File loading test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ File loading test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🧪 Starting comprehensive frontend tests...")
    logger.info("=" * 60)
    
    tests = [
        ("Main Frontend (Port 8503)", test_main_frontend),
        ("Demo Frontend (Port 8504)", test_demo_frontend),
        ("Brain Alignment", test_brain_alignment),
        ("File Loading", test_file_loading)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        logger.info("🎉 ALL TESTS PASSED! Both frontends are ready for use.")
        return 0
    else:
        logger.error("⚠️  Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
