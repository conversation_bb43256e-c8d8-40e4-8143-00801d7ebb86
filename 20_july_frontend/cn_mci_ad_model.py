"""
Enhanced CN/MCI/AD 3-Category Classification Model
Robust model loading with multiple architecture support and comprehensive error handling
"""

import torch
import torch.nn as nn
import numpy as np
import logging
from typing import Dict, Any, Tuple, Optional, List
from pathlib import Path
import warnings

# Import the gated attention model
from gated_attention_cnn import GatedAtten<PERSON><PERSON>N<PERSON>
from cn_mci_ad_preprocessing import CNMCIADPreprocessor

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore', category=UserWarning)

class CNMCIADClassifier:
    """Enhanced 3-category classifier for CN/MCI/AD classification with robust model loading"""

    def __init__(self, model_path: Optional[str] = None):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.model_loaded = False
        self.class_names = ['CN', 'MCI', 'AD']
        self.class_descriptions = {
            'CN': 'Cognitively Normal',
            'MCI': 'Mild Cognitive Impairment',
            'AD': 'Alzheimer\'s Disease'
        }

        # Initialize preprocessor
        self.preprocessor = CNMCIADPreprocessor()

        # Model file paths to try (in order of preference)
        self.model_paths = [
            'gated_attention_cnn_final.pth',  # 300-epoch trained model
            'memory_efficient_cnn_model.pth',  # Alternative model
            model_path  # User-provided path
        ]

        # Try to load model
        self._initialize_model()

        logger.info(f"Classifier initialized on device: {self.device}")
        logger.info(f"Model loaded: {self.model_loaded}")

    def _initialize_model(self):
        """Initialize and load the best available model"""
        try:
            # Try each model path
            for model_path in self.model_paths:
                if model_path and Path(model_path).exists():
                    if self.load_model(model_path):
                        return

            # If no model found, create with random weights
            logger.warning("No trained model found, initializing with random weights")
            self.model = GatedAttentionCNN(num_classes=3)
            self.model.to(self.device)
            self.model.eval()
            self.model_loaded = False

        except Exception as e:
            logger.error(f"Error initializing model: {e}")
            # Create minimal fallback model
            self.model = GatedAttentionCNN(num_classes=3)
            self.model.to(self.device)
            self.model.eval()
            self.model_loaded = False
    
    def load_model(self, model_path: str) -> bool:
        """
        Load trained model weights with comprehensive error handling

        Args:
            model_path: Path to model file

        Returns:
            True if successful, False otherwise
        """
        try:
            model_path = Path(model_path)

            if not model_path.exists():
                logger.warning(f"Model file not found: {model_path}")
                return False

            logger.info(f"Loading model from: {model_path}")

            # Load state dict
            state_dict = torch.load(model_path, map_location=self.device)

            # Initialize model if not already done
            if self.model is None:
                self.model = GatedAttentionCNN(num_classes=3)

            # Load weights
            self.model.load_state_dict(state_dict)
            self.model.to(self.device)
            self.model.eval()

            # Verify model is working
            test_input = torch.randn(1, 1, 64, 64, 64).to(self.device)
            with torch.no_grad():
                test_output = self.model(test_input)
                if test_output.shape != (1, 3):
                    logger.error(f"Model output shape incorrect: {test_output.shape}")
                    return False

            self.model_loaded = True
            logger.info(f"Model loaded successfully from {model_path}")
            logger.info(f"Model size: {model_path.stat().st_size / (1024*1024):.1f} MB")
            return True

        except Exception as e:
            logger.error(f"Failed to load model from {model_path}: {e}")
            return False
    
    def preprocess_mri(self, mri_data: np.ndarray) -> torch.Tensor:
        """
        Preprocess MRI data for model input using the enhanced preprocessor

        Args:
            mri_data: Raw MRI data

        Returns:
            Preprocessed tensor ready for model input
        """
        try:
            # Use the enhanced preprocessor
            processed_data = self.preprocessor.normalize_intensity(mri_data)

            # Ensure 3D
            if processed_data.ndim == 4:
                processed_data = processed_data.squeeze()

            # Resize if needed
            if processed_data.shape != (64, 64, 64):
                processed_data = self.preprocessor.resize_volume(processed_data, (64, 64, 64))

            # Convert to tensor
            tensor = torch.FloatTensor(processed_data).unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
            return tensor.to(self.device)

        except Exception as e:
            logger.error(f"Error preprocessing MRI data: {e}")
            # Fallback to simple preprocessing
            if mri_data.ndim == 4:
                mri_data = mri_data.squeeze()

            from scipy.ndimage import zoom
            target_size = (64, 64, 64)
            if mri_data.shape != target_size:
                zoom_factors = [target_size[i] / mri_data.shape[i] for i in range(3)]
                mri_data = zoom(mri_data, zoom_factors, order=1)

            # Simple normalization
            mri_data = (mri_data - np.mean(mri_data)) / (np.std(mri_data) + 1e-8)

            tensor = torch.FloatTensor(mri_data).unsqueeze(0).unsqueeze(0)
            return tensor.to(self.device)
    
    def predict(self, mri_data: np.ndarray) -> Dict[str, Any]:
        """Make prediction on MRI data"""
        try:
            # Preprocess
            input_tensor = self.preprocess_mri(mri_data)
            
            # Predict
            with torch.no_grad():
                outputs = self.model(input_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                predicted_class = torch.argmax(probabilities, dim=1).item()
            
            # Convert to numpy for easier handling
            probs = probabilities.cpu().numpy()[0]
            
            # Create results
            results = {
                'predicted_class': self.class_names[predicted_class],
                'predicted_class_description': self.class_descriptions[self.class_names[predicted_class]],
                'confidence': float(probs[predicted_class]),
                'probabilities': {
                    self.class_names[i]: float(probs[i]) 
                    for i in range(len(self.class_names))
                },
                'class_probabilities_ordered': [
                    {
                        'class': self.class_names[i],
                        'description': self.class_descriptions[self.class_names[i]],
                        'probability': float(probs[i])
                    }
                    for i in np.argsort(probs)[::-1]  # Sort by probability descending
                ]
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return {
            'model_type': 'Memory-Efficient CNN',
            'classes': self.class_names,
            'class_descriptions': self.class_descriptions,
            'accuracy': '56%',
            'training_data': 'NACC + ADNI datasets',
            'input_size': '64x64x64',
            'device': str(self.device)
        }

# Create a simple function for easy import
def create_classifier(model_path: Optional[str] = None) -> CNMCIADClassifier:
    """Create and return a CN/MCI/AD classifier"""
    return CNMCIADClassifier(model_path)
