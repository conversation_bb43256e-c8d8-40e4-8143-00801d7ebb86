"""
CN/MCI/AD 3-Category Classification Model
Gated Attention CNN trained for 300 epochs on cluster for superior accuracy
"""

import torch
import torch.nn as nn
import numpy as np
import logging
from typing import Dict, Any, Tuple, Optional
from pathlib import Path

# Import the gated attention model
from gated_attention_cnn import GatedAttentionCNN

logger = logging.getLogger(__name__)

class CNMCIADClassifier:
    """3-category classifier for CN/MCI/AD classification using Gated Attention CNN"""

    def __init__(self, model_path: Optional[str] = None):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = GatedAttentionCNN(num_classes=3)
        self.class_names = ['CN', 'MCI', 'AD']
        self.class_descriptions = {
            'CN': 'Cognitively Normal',
            'MCI': 'Mild Cognitive Impairment',
            'AD': 'Alzheimer\'s Disease'
        }

        # Load model if path provided
        if model_path and Path(model_path).exists():
            self.load_model(model_path)
        else:
            logger.warning("No model loaded - using random weights")
    
    def load_model(self, model_path: str):
        """Load trained model weights"""
        try:
            state_dict = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(state_dict)
            self.model.to(self.device)
            self.model.eval()
            logger.info(f"Model loaded successfully from {model_path}")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def preprocess_mri(self, mri_data: np.ndarray) -> torch.Tensor:
        """Preprocess MRI data for model input"""
        # Ensure 3D
        if mri_data.ndim == 4:
            mri_data = mri_data.squeeze()
        
        # Resize to expected input size (64x64x64)
        from scipy.ndimage import zoom
        target_size = (64, 64, 64)
        current_size = mri_data.shape
        zoom_factors = [target_size[i] / current_size[i] for i in range(3)]
        mri_data = zoom(mri_data, zoom_factors, order=1)
        
        # Normalize
        mri_data = (mri_data - np.mean(mri_data)) / (np.std(mri_data) + 1e-8)
        
        # Convert to tensor
        tensor = torch.FloatTensor(mri_data).unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
        return tensor.to(self.device)
    
    def predict(self, mri_data: np.ndarray) -> Dict[str, Any]:
        """Make prediction on MRI data"""
        try:
            # Preprocess
            input_tensor = self.preprocess_mri(mri_data)
            
            # Predict
            with torch.no_grad():
                outputs = self.model(input_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                predicted_class = torch.argmax(probabilities, dim=1).item()
            
            # Convert to numpy for easier handling
            probs = probabilities.cpu().numpy()[0]
            
            # Create results
            results = {
                'predicted_class': self.class_names[predicted_class],
                'predicted_class_description': self.class_descriptions[self.class_names[predicted_class]],
                'confidence': float(probs[predicted_class]),
                'probabilities': {
                    self.class_names[i]: float(probs[i]) 
                    for i in range(len(self.class_names))
                },
                'class_probabilities_ordered': [
                    {
                        'class': self.class_names[i],
                        'description': self.class_descriptions[self.class_names[i]],
                        'probability': float(probs[i])
                    }
                    for i in np.argsort(probs)[::-1]  # Sort by probability descending
                ]
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return {
            'model_type': 'Memory-Efficient CNN',
            'classes': self.class_names,
            'class_descriptions': self.class_descriptions,
            'accuracy': '56%',
            'training_data': 'NACC + ADNI datasets',
            'input_size': '64x64x64',
            'device': str(self.device)
        }

# Create a simple function for easy import
def create_classifier(model_path: Optional[str] = None) -> CNMCIADClassifier:
    """Create and return a CN/MCI/AD classifier"""
    return CNMCIADClassifier(model_path)
