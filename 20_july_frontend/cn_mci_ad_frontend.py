"""
Demetify - CN/MCI/AD 3-Category Classification Frontend
Professional radiologist-focused interface using Gated Attention CNN (300 epochs)
"""

import streamlit as st
import numpy as np
import nibabel as nib
import matplotlib.pyplot as plt
from pathlib import Path
import tempfile
import os
import logging
from typing import Optional, Dict, Any
import time

# Import nilearn for proper MRI visualization
try:
    from nilearn import plotting, image
    from nilearn.plotting import plot_anat, plot_stat_map
    NILEARN_AVAILABLE = True
except ImportError:
    NILEARN_AVAILABLE = False
    st.warning("Nilearn not available. Install with: pip install nilearn")

# Import our custom components
from cn_mci_ad_model import create_classifier
from cn_mci_ad_preprocessing import create_preprocessor
from cn_mci_ad_shap import create_shap_explainer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Demetify - CN/MCI/AD Assessment",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for Demetify branding
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1f4e79 0%, #2d5aa0 100%);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
    }
    .prediction-card {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #2d5aa0;
        margin: 1rem 0;
    }
    .probability-bar {
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin: 0.5rem 0;
    }
    .probability-fill {
        height: 30px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        color: white;
        font-weight: bold;
    }
    .cn-fill { background: #28a745; }
    .mci-fill { background: #ffc107; color: black; }
    .ad-fill { background: #dc3545; }
    .upload-section {
        border: 2px dashed #2d5aa0;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def load_mri_data(uploaded_file) -> Optional[np.ndarray]:
    """Enhanced MRI data loading with comprehensive error handling"""
    try:
        # Determine file type - handle .nii.gz properly
        filename = uploaded_file.name.lower()

        if filename.endswith('.nii.gz'):
            # Handle .nii.gz files
            with tempfile.NamedTemporaryFile(delete=False, suffix='.nii.gz') as tmp_file:
                tmp_file.write(uploaded_file.getvalue())
                tmp_file.flush()

                # Load with nibabel
                nii_img = nib.load(tmp_file.name)
                mri_data = nii_img.get_fdata()

                # Clean up
                os.unlink(tmp_file.name)

                logger.info(f"Loaded NIfTI.gz file: {uploaded_file.name}, shape: {mri_data.shape}")
                return mri_data

        elif filename.endswith('.nii'):
            # Handle .nii files
            with tempfile.NamedTemporaryFile(delete=False, suffix='.nii') as tmp_file:
                tmp_file.write(uploaded_file.getvalue())
                tmp_file.flush()

                # Load with nibabel
                nii_img = nib.load(tmp_file.name)
                mri_data = nii_img.get_fdata()

                # Clean up
                os.unlink(tmp_file.name)

                logger.info(f"Loaded NIfTI file: {uploaded_file.name}, shape: {mri_data.shape}")
                return mri_data

        elif filename.endswith('.npy'):
            # Handle numpy files
            mri_data = np.load(uploaded_file, allow_pickle=True)
            logger.info(f"Loaded numpy file: {uploaded_file.name}, shape: {mri_data.shape}")
            return mri_data

        else:
            st.error(f"Unsupported file format. Please upload .nii, .nii.gz, or .npy files.")
            return None

    except Exception as e:
        st.error(f"Error loading MRI data: {e}")
        logger.error(f"Error loading {uploaded_file.name}: {e}")
        return None

def display_mri_slices(mri_data: np.ndarray, title: str = "MRI Visualization"):
    """Enhanced MRI slice display with proper radiological orientations"""
    try:
        if mri_data.ndim == 4:
            mri_data = mri_data.squeeze()

        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        fig.suptitle(title, fontsize=16, fontweight='bold')

        # Get middle slices
        mid_x, mid_y, mid_z = [s // 2 for s in mri_data.shape]

        # Normalize for display
        mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data) + 1e-8)

        # Sagittal view (Left-Right)
        sagittal_slice = np.rot90(mri_normalized[mid_x, :, :])
        axes[0].imshow(sagittal_slice, cmap='gray', aspect='equal')
        axes[0].set_title('Sagittal View\n(Left-Right)', fontsize=12)
        axes[0].axis('off')

        # Coronal view (Anterior-Posterior)
        coronal_slice = np.rot90(mri_normalized[:, mid_y, :])
        axes[1].imshow(coronal_slice, cmap='gray', aspect='equal')
        axes[1].set_title('Coronal View\n(Anterior-Posterior)', fontsize=12)
        axes[1].axis('off')

        # Axial view (Bottom-Top)
        axial_slice = np.rot90(mri_normalized[:, :, mid_z])
        axes[2].imshow(axial_slice, cmap='gray', aspect='equal')
        axes[2].set_title('Axial View\n(Bottom-Top)', fontsize=12)
        axes[2].axis('off')

        plt.tight_layout()
        return fig

    except Exception as e:
        logger.error(f"Error displaying MRI slices: {e}")
        # Return a simple fallback figure
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.text(0.5, 0.5, f'Error displaying MRI\n{str(e)}',
                ha='center', va='center', transform=ax.transAxes)
        ax.axis('off')
        return fig

def display_prediction_results(results: Dict[str, Any]):
    """Display prediction results with probabilities"""
    st.markdown('<div class="prediction-card">', unsafe_allow_html=True)
    
    # Main prediction
    predicted_class = results['predicted_class']
    confidence = results['confidence']
    description = results['predicted_class_description']
    
    st.markdown(f"### 🎯 Prediction: **{predicted_class}**")
    st.markdown(f"**{description}**")
    st.markdown(f"**Confidence:** {confidence:.1%}")
    
    st.markdown("### 📊 Classification Probabilities")
    
    # Display probability bars for all classes
    for class_info in results['class_probabilities_ordered']:
        class_name = class_info['class']
        prob = class_info['probability']
        desc = class_info['description']
        
        # Determine color class
        color_class = class_name.lower() + '-fill'
        
        st.markdown(f"**{class_name}** - {desc}")
        
        # Create probability bar
        bar_html = f"""
        <div class="probability-bar">
            <div class="probability-fill {color_class}" style="width: {prob*100}%;">
                {prob:.1%}
            </div>
        </div>
        """
        st.markdown(bar_html, unsafe_allow_html=True)
    
    st.markdown('</div>', unsafe_allow_html=True)

def main():
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🧠 Demetify - CN/MCI/AD Assessment</h1>
        <p>Advanced 3-Category Dementia Classification with Gated Attention CNN (300 Epochs)</p>
        <p><strong>University of Illinois at Urbana-Champaign</strong> | Project Lead: Prof. S. Seshadri</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize components with enhanced error handling
    @st.cache_resource
    def load_components():
        """Load all system components with comprehensive error handling"""
        try:
            # Initialize classifier (will try multiple model paths)
            classifier = create_classifier()

            # Display model status
            if classifier.model_loaded:
                st.success("✅ Trained model loaded successfully (300 epochs)")
            else:
                st.warning("⚠️ Using demo mode with random weights")
                st.info("Upload a real MRI scan to see the system capabilities")

            # Initialize explainer
            explainer = create_shap_explainer(classifier)

            return classifier, explainer

        except Exception as e:
            st.error(f"Error loading components: {e}")
            logger.error(f"Component loading error: {e}")
            return None, None

    # Load components
    classifier, explainer = load_components()

    if classifier is None:
        st.error("Failed to initialize system components. Please refresh the page.")
        st.stop()
    
    # Model information
    with st.expander("ℹ️ Model Information"):
        model_info = classifier.get_model_info()
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**Model Type:** {model_info['model_type']}")
            st.write(f"**Input Size:** {model_info['input_size']}")

        with col2:
            st.write(f"**Classes:** {', '.join(model_info['classes'])}")
            st.write(f"**Training Data:** {model_info['training_data']}")
            st.write(f"**Device:** {model_info['device']}")
    
    # File upload section
    st.markdown('<div class="upload-section">', unsafe_allow_html=True)
    st.markdown("### 📁 Upload MRI Scan")
    st.markdown("Upload a T1-weighted MRI scan in NIfTI (.nii, .nii.gz) or NumPy (.npy) format")

    uploaded_file = st.file_uploader(
        "Choose MRI file",
        type=['nii', 'gz', 'npy'],
        help="Upload T1-weighted MRI scan for CN/MCI/AD classification"
    )
    st.markdown('</div>', unsafe_allow_html=True)
    
    if uploaded_file is not None:
        st.success(f"✅ File uploaded: {uploaded_file.name}")
        
        # Load and display MRI data
        with st.spinner("Loading MRI data..."):
            mri_data = load_mri_data(uploaded_file)
        
        if mri_data is not None:
            # Display preprocessing information
            st.markdown("## 📊 Preprocessing Information")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Original Shape", f"{mri_data.shape}")
            with col2:
                st.metric("Data Type", str(mri_data.dtype))
            with col3:
                st.metric("Value Range", f"[{np.min(mri_data):.2f}, {np.max(mri_data):.2f}]")

            # Display MRI visualization
            st.markdown("## 🖼️ MRI Visualization")
            fig = display_mri_slices(mri_data, f"MRI Scan: {uploaded_file.name}")
            st.pyplot(fig)
            
            # Make prediction
            st.markdown("## 🔬 AI Analysis")

            with st.spinner("Analyzing MRI scan..."):
                try:
                    # Get prediction results
                    results = classifier.predict(mri_data)
                    display_prediction_results(results)

                    # Generate interpretability with progress tracking
                    st.markdown("## 🔍 Interpretability Analysis")

                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    status_text.text("Generating explanations for all classes...")
                    progress_bar.progress(20)

                    explanation = explainer.generate_full_explanation(mri_data)
                    progress_bar.progress(60)

                    status_text.text("Creating visualizations...")

                    # Display explanations for each class with enhanced visualization
                    tabs = st.tabs([f"{cls} Analysis" for cls in classifier.class_names])

                    for i, (tab, class_name) in enumerate(zip(tabs, classifier.class_names)):
                        with tab:
                            class_key = f'{class_name.lower()}_explanation'
                            viz_key = f'{class_name.lower()}_visualizations'

                            if class_key in explanation and viz_key in explanation:
                                # Get class probability from main results
                                class_prob = results['probabilities'][class_name]
                                st.write(f"**{class_name} Probability:** {class_prob:.1%}")

                                # Get visualizations
                                viz = explanation[viz_key]

                                # Display enhanced slice visualizations
                                col1, col2, col3 = st.columns(3)

                                views = ['axial', 'sagittal', 'coronal']
                                view_titles = ['Axial (Bottom-Top)', 'Sagittal (Left-Right)', 'Coronal (Anterior-Posterior)']

                                for col, view, title in zip([col1, col2, col3], views, view_titles):
                                    with col:
                                        st.write(f"**{title}**")

                                        if view in viz:
                                            # Get slice data
                                            mri_slice = viz[view]['mri']
                                            heatmap = viz[view]['heatmap']

                                            # Calculate optimal figure size based on slice dimensions
                                            slice_shape = viz[view].get('shape', mri_slice.shape)
                                            aspect_ratio = slice_shape[1] / slice_shape[0]

                                            # Create ultra-high-resolution figure
                                            fig_width = 10
                                            fig_height = fig_width / aspect_ratio
                                            fig, ax = plt.subplots(figsize=(fig_width, fig_height), dpi=200)

                                            # Display MRI slice with maximum quality
                                            ax.imshow(mri_slice, cmap='gray', alpha=0.85,
                                                    interpolation='lanczos', aspect='equal')

                                            # Overlay heatmap with enhanced visibility
                                            if heatmap.ndim == 3 and np.any(heatmap > 0):
                                                # Apply heatmap with optimal blending
                                                ax.imshow(heatmap, alpha=0.8,
                                                        interpolation='lanczos', aspect='equal')

                                            ax.set_title(f"{class_name} Attention Map",
                                                       fontsize=16, fontweight='bold', pad=20)
                                            ax.axis('off')

                                            # Remove all margins for maximum resolution
                                            plt.subplots_adjust(left=0, right=1, top=0.9, bottom=0)

                                            # Display with maximum quality
                                            st.pyplot(fig, use_container_width=True, dpi=200)
                                            plt.close()

                                            # Show slice information
                                            st.caption(f"Resolution: {slice_shape[0]}×{slice_shape[1]} pixels")
                                        else:
                                            st.write("Visualization not available")
                            else:
                                st.write(f"Explanation for {class_name} not available")

                    progress_bar.progress(100)
                    status_text.text("✅ Analysis complete!")

                except Exception as e:
                    st.error(f"Analysis failed: {e}")
                    logger.error(f"Prediction error: {e}")

                    # Show fallback information
                    st.info("The system encountered an issue during analysis. The MRI scan was loaded successfully, but interpretability maps could not be generated.")

                    # Try to show at least the prediction if available
                    try:
                        if 'results' in locals():
                            st.write("**Prediction results were obtained:**")
                            st.json(results)
                    except:
                        pass
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        <p><strong>Demetify</strong> - Advanced Dementia Assessment Tool</p>
        <p>⚠️ <em>For research purposes only. Not for clinical diagnosis.</em></p>
        <p>University of Illinois at Urbana-Champaign | 2025</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
